' ========== 清除Word样式库中的所有自定义样式 ==========
' 功能：清除所有自定义样式，只保留系统自带的基本样式
' 保留样式：标题1-5、正文、无间隔等系统默认样式
' 对于已应用自定义样式的内容，将其改为正文样式后再删除样式

Sub 清除样式()
    Dim doc As Document
    Dim style As style
    Dim i As Integer
    Dim stylesToKeep As String
    Dim deletedCount As Integer
    Dim modifiedCount As Integer

    Set doc = ActiveDocument
    deletedCount = 0
    modifiedCount = 0

    ' 定义要保留的系统自带样式（英文名称）
    stylesToKeep = "|Normal|Heading 1|Heading 2|Heading 3|Heading 4|Heading 5|" & _
                   "|No Spacing|Title|Subtitle|Quote|Intense Quote|List Paragraph|" & _
                   "|Header|Footer|Page Number|Footnote Reference|Footnote Text|" & _
                   "|Endnote Reference|Endnote Text|Hyperlink|FollowedHyperlink|" & _
                   "|Strong|Emphasis|Book Title|TOC Heading|Caption|Index Heading|" & _
                   "|Table of Contents|Bibliography|Document Map|Plain Text|"

    ' 开始处理
    Application.ScreenUpdating = False

    ' 第一步：将所有使用自定义样式的内容改为正文样式
    Call ReplaceCustomStylesWithNormal(doc, stylesToKeep, modifiedCount)

    ' 第二步：删除自定义样式
    Call DeleteCustomStyles(doc, stylesToKeep, deletedCount)

    Application.ScreenUpdating = True

    ' 显示完成消息
    MsgBox "样式清理完成！" & vbCrLf & vbCrLf & _
           "处理结果：" & vbCrLf & _
           "• 修改内容段落数：" & modifiedCount & vbCrLf & _
           "• 删除自定义样式数：" & deletedCount & vbCrLf & vbCrLf & _
           "保留的系统样式：" & vbCrLf & _
           "标题1-5、正文、无间隔等系统默认样式", vbInformation, "样式清理完成"
End Sub

' ========== 将自定义样式的内容改为正文样式 ==========
Sub ReplaceCustomStylesWithNormal(doc As Document, stylesToKeep As String, ByRef modifiedCount As Integer)
    Dim para As Paragraph
    Dim rng As Range
    Dim styleName As String

    ' 处理段落样式
    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal
        ' 如果不是要保留的样式，则改为正文样式
        If InStr(stylesToKeep, "|" & para.style.Name & "|") = 0 And _
           styleName <> "正文" And styleName <> "标题 1" And styleName <> "标题 2" And _
           styleName <> "标题 3" And styleName <> "标题 4" And styleName <> "标题 5" And _
           styleName <> "无间隔" Then
            para.style = "正文"
            modifiedCount = modifiedCount + 1
        End If
    Next para

    ' 处理字符样式（在整个文档范围内查找）
    Set rng = doc.Range
    With rng.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = ""
        .Replacement.Text = ""
        .Forward = True
        .Wrap = wdFindContinue
        .Format = True

        ' 查找并替换字符样式
        For Each style In doc.Styles
            If style.Type = wdStyleTypeCharacter And style.BuiltIn = False Then
                .style = style.Name
                .Replacement.style = "Default Paragraph Font"
                .Execute Replace:=wdReplaceAll
            End If
        Next style
    End With
End Sub

' ========== 删除自定义样式 ==========
Sub DeleteCustomStyles(doc As Document, stylesToKeep As String, ByRef deletedCount As Integer)
    Dim style As style
    Dim i As Integer
    Dim styleName As String
    Dim styleNameLocal As String

    ' 从后往前删除样式，避免索引变化问题
    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)
        styleName = style.Name
        styleNameLocal = style.NameLocal

        ' 检查是否为要保留的系统样式
        If InStr(stylesToKeep, "|" & styleName & "|") = 0 And _
           styleNameLocal <> "正文" And styleNameLocal <> "标题 1" And _
           styleNameLocal <> "标题 2" And styleNameLocal <> "标题 3" And _
           styleNameLocal <> "标题 4" And styleNameLocal <> "标题 5" And _
           styleNameLocal <> "无间隔" And styleNameLocal <> "页眉" And _
           styleNameLocal <> "页脚" And styleNameLocal <> "页码" Then

            ' 尝试删除样式
            On Error Resume Next
            If Not style.BuiltIn Then ' 只删除非内置样式
                style.Delete
                If Err.Number = 0 Then
                    deletedCount = deletedCount + 1
                End If
            End If
            On Error GoTo 0
        End If
    Next i
End Sub

' ========== 完整清理所有样式（包括表格和列表样式）==========
Sub 完整清理样式()
    Dim doc As Document
    Dim style As style
    Dim i As Integer
    Dim deletedCount As Integer
    Dim modifiedCount As Integer
    Dim tableDeletedCount As Integer
    Dim listDeletedCount As Integer

    Set doc = ActiveDocument
    deletedCount = 0
    modifiedCount = 0
    tableDeletedCount = 0
    listDeletedCount = 0

    Application.ScreenUpdating = False

    ' 第一步：处理文档内容的样式
    Call ProcessDocumentContent(doc, modifiedCount)

    ' 第二步：删除段落和字符样式
    Call DeleteParagraphAndCharacterStyles(doc, deletedCount)

    ' 第三步：删除表格样式
    Call DeleteTableStyles(doc, tableDeletedCount)

    ' 第四步：删除列表样式
    Call DeleteListStyles(doc, listDeletedCount)

    Application.ScreenUpdating = True

    ' 显示完成消息
    MsgBox "完整样式清理完成！" & vbCrLf & vbCrLf & _
           "处理结果：" & vbCrLf & _
           "• 修改内容段落数：" & modifiedCount & vbCrLf & _
           "• 删除段落/字符样式：" & deletedCount & vbCrLf & _
           "• 删除表格样式：" & tableDeletedCount & vbCrLf & _
           "• 删除列表样式：" & listDeletedCount & vbCrLf & vbCrLf & _
           "保留样式：标题1-5、正文、无间隔等系统默认样式", vbInformation, "完整样式清理完成"
End Sub

' ========== 处理文档内容样式 ==========
Sub ProcessDocumentContent(doc As Document, ByRef modifiedCount As Integer)
    Dim para As Paragraph
    Dim tbl As Table
    Dim cell As cell
    Dim styleName As String

    ' 处理段落样式
    For Each para In doc.Paragraphs
        styleName = para.style.NameLocal
        If Not IsSystemStyle(styleName) Then
            para.style = "正文"
            modifiedCount = modifiedCount + 1
        End If
    Next para

    ' 处理表格中的段落样式
    For Each tbl In doc.Tables
        For Each cell In tbl.Range.Cells
            For Each para In cell.Range.Paragraphs
                styleName = para.style.NameLocal
                If Not IsSystemStyle(styleName) Then
                    para.style = "正文"
                    modifiedCount = modifiedCount + 1
                End If
            Next para
        Next cell
    Next tbl
End Sub

' ========== 判断是否为系统样式 ==========
Function IsSystemStyle(styleName As String) As Boolean
    Dim systemStyles As String
    systemStyles = "|正文|标题 1|标题 2|标题 3|标题 4|标题 5|无间隔|页眉|页脚|页码|" & _
                   "|脚注引用|脚注文本|尾注引用|尾注文本|超链接|已访问的超链接|" & _
                   "|强调|加强|书名|目录标题|题注|索引标题|目录|参考书目|"

    IsSystemStyle = (InStr(systemStyles, "|" & styleName & "|") > 0)
End Function

' ========== 删除段落和字符样式 ==========
Sub DeleteParagraphAndCharacterStyles(doc As Document, ByRef deletedCount As Integer)
    Dim style As style
    Dim i As Integer

    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)

        If (style.Type = wdStyleTypeParagraph Or style.Type = wdStyleTypeCharacter) And _
           Not style.BuiltIn And Not IsSystemStyle(style.NameLocal) Then

            On Error Resume Next
            style.Delete
            If Err.Number = 0 Then
                deletedCount = deletedCount + 1
            End If
            On Error GoTo 0
        End If
    Next i
End Sub

' ========== 删除表格样式 ==========
Sub DeleteTableStyles(doc As Document, ByRef tableDeletedCount As Integer)
    Dim style As style
    Dim i As Integer

    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)

        If style.Type = wdStyleTypeTable And Not style.BuiltIn Then
            On Error Resume Next
            style.Delete
            If Err.Number = 0 Then
                tableDeletedCount = tableDeletedCount + 1
            End If
            On Error GoTo 0
        End If
    Next i
End Sub

' ========== 删除列表样式 ==========
Sub DeleteListStyles(doc As Document, ByRef listDeletedCount As Integer)
    Dim style As style
    Dim i As Integer

    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)

        If style.Type = wdStyleTypeList And Not style.BuiltIn Then
            On Error Resume Next
            style.Delete
            If Err.Number = 0 Then
                listDeletedCount = listDeletedCount + 1
            End If
            On Error GoTo 0
        End If
    Next i
End Sub

' ========== 快速清理样式（简化版）==========
Sub 快速清理样式()
    Dim doc As Document
    Dim style As style
    Dim para As Paragraph
    Dim i As Integer
    Dim deletedCount As Integer

    Set doc = ActiveDocument
    deletedCount = 0

    ' 确认操作
    If MsgBox("确定要清除所有自定义样式吗？" & vbCrLf & _
              "此操作将：" & vbCrLf & _
              "1. 将所有自定义样式的内容改为正文样式" & vbCrLf & _
              "2. 删除所有自定义样式" & vbCrLf & _
              "3. 只保留系统默认样式", vbYesNo + vbQuestion, "确认清理样式") = vbNo Then
        Exit Sub
    End If

    Application.ScreenUpdating = False

    ' 第一步：将所有非系统样式改为正文样式
    For Each para In doc.Paragraphs
        If Not IsSystemStyle(para.style.NameLocal) Then
            para.style = "正文"
        End If
    Next para

    ' 第二步：删除所有自定义样式
    For i = doc.Styles.Count To 1 Step -1
        Set style = doc.Styles(i)
        If Not style.BuiltIn And Not IsSystemStyle(style.NameLocal) Then
            On Error Resume Next
            style.Delete
            If Err.Number = 0 Then
                deletedCount = deletedCount + 1
            End If
            On Error GoTo 0
        End If
    Next i

    Application.ScreenUpdating = True

    MsgBox "快速清理完成！" & vbCrLf & _
           "删除了 " & deletedCount & " 个自定义样式", vbInformation, "清理完成"
End Sub