'标题：章一章、一、（一）、1、（1）、a.编号
Sub ChangeParagraphStyle()
    ' 重置大纲编号库中的第5个编号模板，避免旧设置影响
    ListGalleries(wdOutlineNumberGallery).Reset (5) ' 重置第5个大纲编号模板

    ' ========== 标题1 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(1) ' 设置第1级标题编号格式
        .NumberFormat = "第%1章 " ' 编号格式为"第X章"
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进为0厘米
        .Alignment = wdListLevelAlignCenter ' 编号居中对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进为0厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 0 ' 不受更高级别影响
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 1" ' 关联到"标题 1"样式
    End With
    With ActiveDocument.Styles("标题 1").Font ' 设置标题1字体
        .NameFarEast = "黑体" ' 中文字体黑体
        .NameAscii = "黑体" ' 英文及数字字体黑体
        .NameOther = "黑体" ' 其他语言字体黑体
        .Name = "黑体" ' 字体名称
        .Size = 15 ' 字号15磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 1").ParagraphFormat ' 设置标题1段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中
        .SpaceBefore = 24 ' 段前24磅
        .SpaceAfter = 18 ' 段后18磅
    End With

    ' ========== 标题2 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(2) ' 设置第2级标题编号格式
        .NumberFormat = "%2、 " ' 编号格式为"X、"
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0) ' 编号左缩进为0厘米
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(0) ' 标题文本缩进为0厘米
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 1 ' 受更高级别影响，当上级标题变化时重置编号
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 2" ' 关联到"标题 2"样式
    End With
    With ActiveDocument.Styles("标题 2").Font ' 设置标题2字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 14 ' 字号14磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 2").ParagraphFormat ' 设置标题2段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 12 ' 段前12磅
        .SpaceAfter = 6 ' 段后6磅
    End With

    ' ========== 标题3 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(3) ' 设置第3级标题编号格式
        .NumberFormat = "（%3）" ' 编号格式为"(X)"
        .NumberStyle = 37 ' 中文大写数字编号样式
        .NumberPosition = CentimetersToPoints(0.74) ' 编号左缩进0.74厘米（2字符）
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(0.74) ' 标题文本缩进0.74厘米（2字符）
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 2 ' 受更高级别影响，当上级标题变化时重置编号
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 3" ' 关联到"标题 3"样式
    End With
    With ActiveDocument.Styles("标题 3").Font ' 设置标题3字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 3").ParagraphFormat ' 设置标题3段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 3 ' 段后3磅
    End With

    ' ========== 标题4 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(4) ' 设置第4级标题编号格式
        .NumberFormat = "%4. " ' 编号格式为"X."
        .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        .NumberPosition = CentimetersToPoints(1.11) ' 编号左缩进1.11厘米（3字符）
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(1.75) ' 标题文本缩进1.11厘米（3字符）
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 3 ' 受更高级别影响，当上级标题变化时重置编号
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 4" ' 关联到"标题 4"样式
    End With
    With ActiveDocument.Styles("标题 4").Font ' 设置标题4字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = True ' 加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 4").ParagraphFormat ' 设置标题4段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 6 ' 段后6磅
    End With

    ' ========== 标题5 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(5) ' 设置第5级标题编号格式
        .NumberFormat = "(%5) " ' 编号格式为"(X)"
        .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        .NumberPosition = CentimetersToPoints(1) ' 编号左缩进1.48厘米（4字符）
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(0)   ' 标题文本缩进1.48厘米（4字符）
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 4 ' 受更高级别影响，当上级标题变化时重置编号
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 5" ' 关联到"标题 5"样式
    End With
    With ActiveDocument.Styles("标题 5").Font ' 设置标题5字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 5").ParagraphFormat ' 设置标题5段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 6 ' 段后6磅
    End With

    ' ========== 标题6 ==========
    With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(6) ' 设置第6级标题编号格式
        .NumberFormat = "%6. " ' 编号格式为"a."
        .NumberStyle = wdListNumberStyleLowercaseLetter ' 小写字母编号样式
        .NumberPosition = CentimetersToPoints(1.78) ' 编号左缩进1.48厘米（4字符）
        .Alignment = wdListLevelAlignLeft ' 编号左对齐
        .TextPosition = CentimetersToPoints(2.44) ' 标题文本缩进1.48厘米（4字符）
        .TabPosition = wdUndefined ' 制表位未定义
        .ResetOnHigher = 5 ' 受更高级别影响，当上级标题变化时重置编号
        .StartAt = 1 ' 从1开始编号
        .LinkedStyle = "标题 6" ' 关联到"标题 6"样式
    End With
    With ActiveDocument.Styles("标题 6").Font ' 设置标题6字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("标题 6").ParagraphFormat ' 设置标题6段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 6 ' 段前6磅
        .SpaceAfter = 6 ' 段后6磅
    End With

    ' ========== 标题7 标题8 标题9==========
    Dim i As Integer ' 定义循环变量
    For i = 7 To 9 ' 循环设置7~9级标题编号样式
        With ListGalleries(wdOutlineNumberGallery).ListTemplates(5).ListLevels(i)
            .NumberFormat = "" ' 不设置编号格式
            .NumberStyle = wdListNumberStyleArabic ' 阿拉伯数字编号样式
        End With
    Next i

    ' 设置模板名称为空，避免影响
    ListGalleries(wdOutlineNumberGallery).ListTemplates(5).Name = ""
    ' 应用设置好的多级列表模板到选区
    Selection.Range.ListFormat.ApplyListTemplateWithLevel ListTemplate:= _
        ListGalleries(wdOutlineNumberGallery).ListTemplates(5), _
        ContinuePreviousList:=True, ApplyTo:=wdListApplyToWholeList, _
        DefaultListBehavior:=wdWord10ListBehavior

    ' ========== 设置快捷键 ==========
    Call SetHeadingShortcuts

    ' ========== 正文样式设置 ==========
    With ActiveDocument.Styles("正文").Font ' 设置正文字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("正文").ParagraphFormat ' 设置正文段落格式
        .Alignment = wdAlignParagraphLeft ' 段落左对齐
        .SpaceBefore = 0 ' 段前0磅
        .SpaceAfter = 6 ' 段后6磅
        .LineSpacingRule = wdLineSpaceMultiple ' 多倍行距
        .LineSpacing = 1.2 ' 1.2倍行距
        .FirstLineIndent = CentimetersToPoints(0.74) ' 首行缩进2字符（0.74厘米）
        .LeftIndent = 0 ' 左缩进0
        .RightIndent = 0 ' 右缩进0
    End With

    ' ========== 无间隔样式设置 ==========
    With ActiveDocument.Styles("无间隔").Font ' 设置无间隔样式字体
        .NameFarEast = "宋体" ' 中文字体宋体
        .NameAscii = "宋体" ' 英文及数字字体宋体
        .NameOther = "宋体" ' 其他语言字体宋体
        .Name = "宋体" ' 字体名称
        .Size = 12 ' 字号12磅（小四号）
        .Bold = False ' 不加粗
        .Color = wdColorBlack ' 黑色
    End With
    With ActiveDocument.Styles("无间隔").ParagraphFormat ' 设置无间隔样式段落格式
        .Alignment = wdAlignParagraphCenter ' 段落居中对齐
    End With

    ' 显示设置完成消息
    MsgBox "标题样式和快捷键设置完成！" & vbCrLf & vbCrLf & _
           "快捷键设置：" & vbCrLf & _
           "Alt+1 = 标题 1" & vbCrLf & _
           "Alt+2 = 标题 2" & vbCrLf & _
           "Alt+3 = 标题 3" & vbCrLf & _
           "Alt+4 = 标题 4" & vbCrLf & _
           "Alt+5 = 标题 5", vbInformation, "设置完成"

End Sub

' ========== 设置标题快捷键函数 ==========
Sub SetHeadingShortcuts()
    ' 设置自定义上下文为当前文档的模板
    CustomizationContext = ActiveDocument.AttachedTemplate

    ' 错误处理：如果快捷键已存在，先清除再重新设置
    On Error Resume Next

    ' 清除可能存在的旧快捷键绑定
    Dim kb As KeyBinding
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey1))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey2))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey3))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey4))
    If Not kb Is Nothing Then kb.Clear
    Set kb = KeyBindings.Key(BuildKeyCode(wdKeyAlt, wdKey5))
    If Not kb Is Nothing Then kb.Clear

    ' 设置新的快捷键绑定
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey1), KeyCategory:=wdKeyCategoryStyle, Command:="标题 1"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey2), KeyCategory:=wdKeyCategoryStyle, Command:="标题 2"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey3), KeyCategory:=wdKeyCategoryStyle, Command:="标题 3"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey4), KeyCategory:=wdKeyCategoryStyle, Command:="标题 4"
    KeyBindings.Add KeyCode:=BuildKeyCode(wdKeyAlt, wdKey5), KeyCategory:=wdKeyCategoryStyle, Command:="标题 5"

    On Error GoTo 0
End Sub

